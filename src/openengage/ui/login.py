"""
Login and signup UI component for OpenEngage.
"""
import os
import json
import streamlit as st

def display_login_signup():
    """Display the login and signup interface"""
    # Add custom CSS for form styling
    st.markdown("""
        <style>
        .auth-form {
            max-width: 450px;
            margin: 0 auto;
            padding: 1.5rem;
            border-radius: 10px;
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .auth-form .stButton > button {
            width: 100%;
        }
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
            color: #333333;
            font-size: 1.8rem;
            font-weight: 600;
        }
        .auth-footer {
            text-align: center;
            margin-top: 1rem;
        }
        </style>
    """, unsafe_allow_html=True)

    if st.session_state.is_logged_in and not st.session_state.show_dashboard:
        # Center the welcome message and button
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.success(f"Welcome back, {st.session_state.current_user['name']}!")
            if st.button("🚀 Take me to the Dashboards", type="primary", use_container_width=True):
                st.session_state.show_dashboard = True
                st.session_state.show_org_editor = True
                st.session_state.show_login_signup = False
                st.rerun()
        return

    # Center the main content
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown('<div class="auth-header">🔐 Login & Sign Up</div>', unsafe_allow_html=True)

        if "show_signup" not in st.session_state:
            st.session_state.show_signup = False

        if "login_attempts" not in st.session_state:
            st.session_state.login_attempts = 0

        if st.session_state.show_signup:
            # Signup Form
            st.markdown('<div class="auth-form">', unsafe_allow_html=True)
            st.markdown('<h3 style="text-align: center;">Create New Account</h3>', unsafe_allow_html=True)

            # Back button
            if st.button("← Back", use_container_width=True):
                st.session_state.show_signup = False
                st.rerun()

            with st.form("signup_form", clear_on_submit=True):
                email = st.text_input("Email ID*")
                user_name = st.text_input("Name*")
                org_name = st.text_input("Organization Name*")
                org_url = st.text_input("Organization URL*")
                num_employees = st.number_input("Number of Employees*", min_value=1, value=1)

                # Country code and phone in same line
                col1, col2 = st.columns([1, 3])
                with col1:
                    country_code = st.text_input("Code", placeholder="+1")
                with col2:
                    phone = st.text_input("Phone Number (Optional)")

                password = st.text_input("Password*", type="password")
                confirm_password = st.text_input("Confirm Password*", type="password")

                submitted = st.form_submit_button("Sign Up", use_container_width=True)

            st.markdown('</div>', unsafe_allow_html=True)

            if submitted:
                if not all([email, user_name, org_name, org_url, password, confirm_password]):
                    st.error("Please fill all required fields marked with *")
                elif password != confirm_password:
                    st.error("Passwords do not match!")
                else:
                    # Create user data
                    user_data = {
                        "email": email,
                        "name": user_name,
                        "organization": {
                            "name": org_name,
                            "url": org_url,
                            "employees": num_employees
                        },
                        "contact": {
                            "country_code": country_code,
                            "phone": phone
                        },
                        "password": password  # In production, this should be hashed
                    }

                    # Load existing users
                    users = []
                    try:
                        with open('data/users.json', 'r') as f:
                            users = json.load(f)
                    except (FileNotFoundError, json.JSONDecodeError):
                        users = []

                    # Check if email already exists
                    if any(user.get('email') == email for user in users):
                        st.error("Email already registered!")
                        return

                    # Add new user
                    users.append(user_data)

                    # Save updated users
                    with open('data/users.json', 'w') as f:
                        json.dump(users, f, indent=4)

                    st.success("Sign up successful! Please log in.")
                    st.session_state.show_signup = False
                    st.rerun()
        else:
            # Login Form
            st.markdown('<div class="auth-form">', unsafe_allow_html=True)
            st.markdown('<h3 style="text-align: center;">Login</h3>', unsafe_allow_html=True)

            with st.form("login_form", clear_on_submit=True):
                email = st.text_input("Email ID")
                password = st.text_input("Password", type="password")
                submitted = st.form_submit_button("Login", use_container_width=True)

            # Handle login submission
            if submitted:
                if not email or not password:
                    st.error("Please enter both email and password")
                else:
                    # Load users
                    try:
                        with open('data/users.json', 'r') as f:
                            users = json.load(f)

                        # Find user
                        user = next((u for u in users if u.get('email') == email), None)

                        if user and user.get('password') == password:
                            st.success("Login successful!")
                            st.session_state.login_attempts = 0
                            st.session_state.is_logged_in = True
                            st.session_state.current_user = user
                            # Store organization URL and name in session state
                            user_org = user.get('organization', {})
                            st.session_state.organization_url = user_org.get('url', '')
                            st.session_state.organization_name = user_org.get('name', 'OpenEngage Team')

                            # Set up to show Brand & Product Setup screen immediately
                            st.session_state.show_editor = True
                            if hasattr(st.session_state, 'reset_other_views'):
                                st.session_state.reset_other_views('show_editor')

                            # If the organization URL exists, set flag to check for existing data
                            if st.session_state.organization_url:
                                # Initialize crew if not already done
                                if not hasattr(st.session_state, 'crew'):
                                    from crew_manager import OpenEngageCrew
                                    st.session_state.crew = OpenEngageCrew()

                                # Set organization URL in crew
                                st.session_state.crew.organization_url = st.session_state.organization_url

                                # Check for existing organization data using the utility function
                                from utils.file_utils import load_organization_data

                                # Load organization data for this URL
                                org_data = load_organization_data(st.session_state.organization_url)

                                if org_data:
                                    # Organization data exists, set flag to ask about updating
                                    st.session_state.existing_org_data = org_data
                                    st.session_state.show_org_update_option = True
                                else:
                                    # Organization not found, set flag for analysis
                                    st.session_state.analyzing_organization = True

                            st.rerun()
                        else:
                            st.session_state.login_attempts += 1
                            st.error(f"Authentication failed! Attempt {st.session_state.login_attempts}")
                    except (FileNotFoundError, json.JSONDecodeError):
                        st.error("No registered users found!")

            st.markdown('</div>', unsafe_allow_html=True)

            # Sign up section - only show if not logged in
            if not st.session_state.is_logged_in:
                st.markdown('<div class="auth-footer">', unsafe_allow_html=True)
                if st.button("Sign Up", use_container_width=True):
                    st.session_state.show_signup = True
                    st.rerun()
                st.write("Don't have an account? Click Sign Up to create one.")
                st.markdown('</div>', unsafe_allow_html=True)
