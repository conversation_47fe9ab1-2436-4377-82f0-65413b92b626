"""
UI component for template pruning configuration settings.
"""
import streamlit as st
import os
import json
import glob
from config.pruning_config import get_pruning_config, get_pruning_descriptions, save_pruning_config

def render_pruning_config():
    """Render the pruning configuration UI component."""
    st.header("🔍 Pruning Configuration")
    
    # Get current configuration and descriptions
    config = get_pruning_config()
    descriptions = get_pruning_descriptions()
    
    # Create a form for the configuration settings
    with st.form("pruning_config_form"):
        st.write("Configure template pruning parameters used for performance analysis:")
        
        # Required volume threshold
        volume_threshold = st.number_input(
            "Required Volume Threshold",
            min_value=100,
            max_value=100000,
            value=config["required_volume_threshold"],
            step=100,
            help=descriptions["required_volume_threshold"]
        )
        
        # Percentile threshold
        percentile_threshold = st.slider(
            "Percentile Threshold for Pruning",
            min_value=0.0,
            max_value=1.0,
            value=config["percentile_threshold"],
            step=0.05,
            format="%.2f",
            help=descriptions["percentile_threshold"]
        )
        
        # Minimum template count
        min_template_count = st.number_input(
            "Minimum Template Count per Stage-Product",
            min_value=1,
            max_value=20,
            value=config["min_template_count"],
            step=1,
            help=descriptions["min_template_count"]
        )
        
        # Save button
        submitted = st.form_submit_button("Save Configuration")
        
        if submitted:
            # Save the configuration values
            new_config = {
                "required_volume_threshold": volume_threshold,
                "percentile_threshold": percentile_threshold,
                "min_template_count": min_template_count
            }
            
            if save_pruning_config(new_config):
                st.success("Configuration saved successfully!")
            else:
                st.error("Failed to save configuration. Please try again.")
    
    # Add spacing
    st.write("---")
    
    # Display pruned templates section
    st.subheader("📊 Pruned Templates")
    display_pruned_templates()

def display_pruned_templates():
    """Display pruned templates in an expander"""
    st.write("Templates that have been flagged for pruning based on current configuration settings.")
    
    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

    # Get list of template files
    template_files = glob.glob('data/templates/*.json')

    if not template_files:
        st.warning("No templates found. Please generate templates first.")
        return

    # Create tabs for each stage
    stage_names = [os.path.basename(f).replace('.json', '').replace('_', ' ').title() for f in template_files]
    tabs = st.tabs(stage_names)

    # Track if any pruned templates were found across all tabs
    any_pruned_templates_found = False

    # Display pruned templates for each stage in its tab
    for tab, template_file in zip(tabs, template_files):
        with tab:
            pruned_templates_found = False
            try:
                with open(template_file, 'r') as f:
                    templates = json.load(f)

                # Filter templates by organization URL if enabled
                if org_filter_enabled and org_url:
                    templates = [
                        t for t in templates
                        if t.get('product_data', {}).get('Company_URL', '') == org_url
                    ]

                if not templates:
                    if org_filter_enabled and org_url:
                        st.warning(f"No templates found for this stage in your organization ({org_url}).")
                    else:
                        st.warning(f"No templates found for this stage.")
                    continue
                
                # Filter only pruned templates (pruning_flag = 1)
                pruned_templates = [t for t in templates if t.get('performance', {}).get('pruning_flag', 0) == 1]
                
                if not pruned_templates:
                    st.info("No pruned templates found for this stage.")
                    continue
                
                pruned_templates_found = True
                any_pruned_templates_found = True
                
                # Display each pruned template
                for idx, template_data in enumerate(pruned_templates):
                    template = template_data.get('template', {})
                    template_name = template_data.get('template_name', f'Template {idx + 1}')
                    
                    # Create a container for each template
                    with st.expander(f"📧 {template_name}", expanded=False):
                        # Display template details
                        st.write("**Subject:**", template.get('subject', 'No subject'))
                        
                        # Display performance metrics
                        performance = template_data.get('performance', {})
                        metrics_col1, metrics_col2 = st.columns(2)
                        
                        with metrics_col1:
                            st.write("**Performance Metrics:**")
                            st.write(f"📊 Total Sent: {performance.get('total_sent', 0):,}")
                            st.write(f"👁️ Open Rate: {performance.get('open_rate', 0):.2f}%")
                            st.write(f"🖱️ Click Rate: {performance.get('click_rate', 0):.2f}%")
                        
                        with metrics_col2:
                            st.write("**Product & Stage:**")
                            product_name = template_data.get('product_data', {}).get('Product_Name', 'Unknown')
                            st.write(f"📦 Product: {product_name}")
                            st.write(f"🔄 Stage: {template_data.get('stage', 'Unknown')}")
                            st.write(f"✉️ Click-to-Open: {performance.get('click_to_open_rate', 0):.2f}%")
                        
                        # Display body content
                        st.write("**Body:**")
                        st.text_area("Template Content", value=template.get('body', 'No content'), height=200, key=f"body_{template_file}_{idx}", disabled=True)
                        
                        # Add option to restore template (remove pruning flag)
                        if st.button("🔄 Restore Template (Remove Pruning Flag)", key=f"restore_{template_file}_{idx}"):
                            # Find the template in the original list and update pruning flag
                            for t in templates:
                                if t.get('template_name') == template_name:
                                    if 'performance' not in t:
                                        t['performance'] = {}
                                    t['performance']['pruning_flag'] = 0
                                    
                                    # Save updated template back to file
                                    with open(template_file, 'w') as f:
                                        json.dump(templates, f, indent=4)
                                    
                                    st.success(f"Template '{template_name}' has been restored and is no longer marked for pruning.")
                                    st.rerun()
                                    break
            except Exception as e:
                st.error(f"Error loading templates: {str(e)}")
    
    if not any_pruned_templates_found:
        st.info("No pruned templates found across any stages.")

def add_to_navigation():
    """Add pruning configuration to the navigation menu."""
    # This function would be used to register this page in the app's navigation
    # Implementation depends on the navigation system of the application
    return {
        "name": "Pruning Configuration",
        "icon": "gear",
        "path": "/pruning-config"
    }
