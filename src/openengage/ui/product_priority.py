"""
Product priority UI component for OpenEngage.
"""
import json
import streamlit as st
import pandas as pd
from utils.file_utils import get_all_products

def display_product_priority():
    """Display the product priority management interface"""
    st.markdown("""
        <div style='background-color: #8D06FE; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;'>
            <h2 style='color: #FFFFFF; margin: 0; font-size: 1.2rem;'>Product Priority Management</h2>
            <p style='color: #FFFFFF; margin: 0.5rem 0 0 0; font-size: 1rem;'>
                Set priorities for your organization's products.
            </p>
        </div>
    """, unsafe_allow_html=True)

    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)
    elif hasattr(st.session_state, 'organization_url'):
        org_url = st.session_state.organization_url

    if not org_url:
        st.error("Organization URL not found. Please complete organization setup first.")
        return

    # Load products with optional filtering
    try:
        # Get all products
        all_products = get_all_products()

        # Filter products by organization URL
        if org_filter_enabled:
            org_products = [p for p in all_products if p.get("Company_URL", "") == org_url or
                                                     p.get("organization_url", "") == org_url]
        else:
            # If filter is disabled, still filter by organization URL for this specific module
            # as it's meant to manage priorities for a specific organization
            org_products = [p for p in all_products if p.get("Company_URL", "") == org_url or
                                                     p.get("organization_url", "") == org_url]

        if not org_products:
            st.info("No products found for your organization. Please add products first.")
            return

        # Add default priority if not already present
        for i, product in enumerate(org_products, 1):
            if "Priority" not in product:
                product["Priority"] = i

        # Create DataFrame for display
        product_data = []
        for i, product in enumerate(org_products, 1):
            product_data.append({
                "S. No.": i,
                "Name of Product": product.get("Product_Name", "Unknown"),
                "URL of Product": product.get("Product_URL", "Unknown"),
                "Priority": product.get("Priority", i)
            })

        # Create a data editor for priorities
        st.write("### Product Priorities")
        st.write("Review and adjust the priority of your products:")

        # Create DataFrame with priority columns
        df = pd.DataFrame(product_data)
        edited_df = st.data_editor(
            df,
            column_config={
                "S. No.": st.column_config.NumberColumn(
                    "S. No.",
                    help="Serial number",
                    disabled=True,
                    width="small"
                ),
                "Name of Product": st.column_config.TextColumn(
                    "Name of Product",
                    help="Product name",
                    disabled=True,
                    width="medium"
                ),
                "URL of Product": st.column_config.TextColumn(
                    "URL of Product",
                    help="Product URL",
                    disabled=True,
                    width="medium"
                ),
                "Priority": st.column_config.NumberColumn(
                    "Priority",
                    help="Set priority (lower numbers = higher priority)",
                    min_value=1,
                    max_value=len(org_products),
                    step=1,
                    width="small"
                )
            },
            hide_index=True,
            key="priority_editor"
        )

        # Save button
        if st.button("Save Product Priorities", type="primary"):
            # Get the edited priorities
            edited_priorities = edited_df["Priority"].tolist()

            # Update priorities in products and save
            for product, priority in zip(org_products, edited_priorities):
                product["Priority"] = int(priority)

            # Update product details in main list
            for org_product in org_products:
                for i, product in enumerate(all_products):
                    if product.get("Product_Name") == org_product.get("Product_Name"):
                        all_products[i] = org_product
                        break

            # Save to file
            with open("data/product_details.json", "w") as f:
                json.dump(all_products, f, indent=4)

            # Show success message
            success_placeholder = st.empty()
            success_placeholder.success("✅ Product priorities saved successfully! Redirecting to Templates Generator...")

            # Set up redirection after 3 seconds
            import time
            time.sleep(3)

            # Reset current view and set templates generator view
            st.session_state.show_product_priority = False
            st.session_state.show_templates_generator = True
            st.rerun()

    except (FileNotFoundError, json.JSONDecodeError) as e:
        st.error(f"Error loading product data: {str(e)}")
