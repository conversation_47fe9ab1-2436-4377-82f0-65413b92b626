"""
CTA (Call-to-Action) Generator for OpenEngage.
Generates optimized CTAs for email templates using AI with parallel processing.
"""
import os
import json
import glob
import time
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dotenv import load_dotenv
from openai import OpenAI
import streamlit as st

# Load environment variables
load_dotenv()

class CTAGenerator:
    """
    CTA Generator class that handles generating call-to-action buttons
    for email templates using OpenAI's GPT-4o-mini model.
    """
    
    def __init__(self):
        """Initialize the CTA Generator with OpenAI client."""
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.model = "gpt-4o-mini"
        
    def generate_single_cta(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a single CTA for a given template.
        
        Args:
            template_data: Dictionary containing template information
            
        Returns:
            Dictionary with generated CTA information
        """
        try:
            # Extract template information
            template = template_data.get('template', {})
            product_data = template_data.get('product_data', {})
            settings = template_data.get('settings', {})
            stage = template_data.get('stage', 'Unknown')
            
            # Get email body and subject
            email_body = template.get('body', '')
            email_subject = template.get('subject', '')
            product_name = product_data.get('Product_Name', 'Product')
            product_url = product_data.get('Product_URL', '')
            company_name = product_data.get('Company_Name', 'Company')
            
            # Create prompt for CTA generation
            system_prompt = """You are an expert email marketing specialist focused on creating high-converting call-to-action (CTA) buttons. 
            Your task is to analyze the email template and generate an optimized CTA that:
            
            1. Aligns with the email content and user journey stage
            2. Creates urgency and drives action
            3. Is clear, concise, and compelling
            4. Matches the brand tone and style
            5. Is optimized for the specific product/service
            
            Return your response in JSON format with these exact keys:
            {
                "cta_text": "The main CTA button text (max 4 words)",
                "cta_url": "The URL the CTA should link to",
                "cta_style": "primary|secondary|outline",
                "alt_cta_text": "Alternative CTA text option",
                "reasoning": "Brief explanation of why this CTA works for this template"
            }"""
            
            user_prompt = f"""
            Analyze this email template and generate an optimized CTA:
            
            **Email Subject:** {email_subject}
            
            **Email Body:** {email_body}
            
            **Product Information:**
            - Product Name: {product_name}
            - Company: {company_name}
            - Product URL: {product_url}
            - User Journey Stage: {stage}
            
            **Brand Settings:**
            - Tone: {settings.get('tone_of_voice', 'Professional')}
            - Style: {settings.get('style', 'formal')}
            - Brand Personality: {settings.get('brand_personality', 'Professional')}
            
            Generate a CTA that will maximize conversions for this specific template and user stage.
            """
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=300,
                response_format={"type": "json_object"}
            )
            
            # Parse response
            cta_data = json.loads(response.choices[0].message.content)
            
            # Add metadata
            cta_data.update({
                "generated_at": datetime.now().isoformat(),
                "template_name": template_data.get('template_name', 'Unknown'),
                "stage": stage,
                "success": True,
                "error": None
            })
            
            return cta_data
            
        except Exception as e:
            # Return error information
            return {
                "cta_text": "Learn More",  # Fallback CTA
                "cta_url": product_data.get('Product_URL', ''),
                "cta_style": "primary",
                "alt_cta_text": "Get Started",
                "reasoning": f"Error generating CTA: {str(e)}",
                "generated_at": datetime.now().isoformat(),
                "template_name": template_data.get('template_name', 'Unknown'),
                "stage": template_data.get('stage', 'Unknown'),
                "success": False,
                "error": str(e)
            }
    
    def generate_ctas_parallel(
        self, 
        templates: List[Dict[str, Any]], 
        progress_callback: Optional[Callable] = None,
        max_workers: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Generate CTAs for multiple templates using parallel processing.
        
        Args:
            templates: List of template dictionaries
            progress_callback: Function to report progress (current, total, message)
            max_workers: Maximum number of parallel workers
            
        Returns:
            List of dictionaries with CTA information for each template
        """
        total_templates = len(templates)
        results = []
        processed_count = 0
        
        if progress_callback:
            progress_callback(0, total_templates, "Starting CTA generation...")
        
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_template = {
                executor.submit(self.generate_single_cta, template): template
                for template in templates
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_template):
                template = future_to_template[future]
                try:
                    result = future.result()
                    results.append(result)
                    processed_count += 1
                    
                    # Report progress
                    if progress_callback:
                        template_name = template.get('template_name', f'Template {processed_count}')
                        progress_callback(
                            processed_count - 1, 
                            total_templates, 
                            f"Generated CTA for {template_name}"
                        )
                        
                except Exception as e:
                    # Handle individual template errors
                    error_result = {
                        "cta_text": "Learn More",
                        "cta_url": template.get('product_data', {}).get('Product_URL', ''),
                        "cta_style": "primary",
                        "alt_cta_text": "Get Started",
                        "reasoning": f"Error generating CTA: {str(e)}",
                        "generated_at": datetime.now().isoformat(),
                        "template_name": template.get('template_name', 'Unknown'),
                        "stage": template.get('stage', 'Unknown'),
                        "success": False,
                        "error": str(e)
                    }
                    results.append(error_result)
                    processed_count += 1
                    
                    if progress_callback:
                        progress_callback(
                            processed_count - 1, 
                            total_templates, 
                            f"Error processing template: {str(e)}"
                        )
        
        # Final progress update
        if progress_callback:
            progress_callback(total_templates, total_templates, "CTA generation completed!")
        
        return results
