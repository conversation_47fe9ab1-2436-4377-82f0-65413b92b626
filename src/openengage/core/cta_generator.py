"""
CTA (Call-to-Action) Generator for OpenEngage.
Generates optimized CTAs for email templates using AI with parallel processing.
"""
import os
import json
import glob
import time
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dotenv import load_dotenv
from openai import OpenAI
import streamlit as st

# Load environment variables
load_dotenv()

class CTAGenerator:
    """
    CTA Generator class that handles generating call-to-action buttons
    for email templates using OpenAI's GPT-4o-mini model.
    """
    
    def __init__(self):
        """Initialize the CTA Generator with OpenAI client."""
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.model = "gpt-4o-mini"
        
    def generate_single_cta(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a single CTA for a given template.
        
        Args:
            template_data: Dictionary containing template information
            
        Returns:
            Dictionary with generated CTA information
        """
        try:
            # Extract template information
            template = template_data.get('template', {})
            product_data = template_data.get('product_data', {})
            settings = template_data.get('settings', {})
            stage = template_data.get('stage', 'Unknown')
            
            # Get email body and subject
            email_body = template.get('body', '')
            email_subject = template.get('subject', '')
            product_name = product_data.get('Product_Name', 'Product')
            product_url = product_data.get('Product_URL', '')
            company_name = product_data.get('Company_Name', 'Company')
            
            # Create prompt for CTA generation
            system_prompt = """You are an expert email marketing specialist focused on creating high-converting call-to-action (CTA) buttons. 
            Your task is to analyze the email template and generate an optimized CTA that:
            
            1. Aligns with the email content and user journey stage
            2. Creates urgency and drives action
            3. Is clear, concise, and compelling
            4. Matches the brand tone and style
            5. Is optimized for the specific product/service
            
            Return your response in JSON format with these exact keys:
            {
                "cta_text": "The main CTA button text (max 4 words)",
                "cta_url": "The URL the CTA should link to",
                "cta_style": "primary|secondary|outline",
                "alt_cta_text": "Alternative CTA text option",
                "reasoning": "Brief explanation of why this CTA works for this template"
            }"""
            
            user_prompt = f"""
            Analyze this email template and generate an optimized CTA:
            
            **Email Subject:** {email_subject}
            
            **Email Body:** {email_body}
            
            **Product Information:**
            - Product Name: {product_name}
            - Company: {company_name}
            - Product URL: {product_url}
            - User Journey Stage: {stage}
            
            **Brand Settings:**
            - Tone: {settings.get('tone_of_voice', 'Professional')}
            - Style: {settings.get('style', 'formal')}
            - Brand Personality: {settings.get('brand_personality', 'Professional')}
            
            Generate a CTA that will maximize conversions for this specific template and user stage.
            """
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=300,
                response_format={"type": "json_object"}
            )
            
            # Parse response
            cta_data = json.loads(response.choices[0].message.content)
            
            # Add metadata
            cta_data.update({
                "generated_at": datetime.now().isoformat(),
                "template_name": template_data.get('template_name', 'Unknown'),
                "stage": stage,
                "success": True,
                "error": None
            })
            
            return cta_data
            
        except Exception as e:
            # Return error information
            return {
                "cta_text": "Learn More",  # Fallback CTA
                "cta_url": product_data.get('Product_URL', ''),
                "cta_style": "primary",
                "alt_cta_text": "Get Started",
                "reasoning": f"Error generating CTA: {str(e)}",
                "generated_at": datetime.now().isoformat(),
                "template_name": template_data.get('template_name', 'Unknown'),
                "stage": template_data.get('stage', 'Unknown'),
                "success": False,
                "error": str(e)
            }
    
    def generate_ctas_parallel(
        self, 
        templates: List[Dict[str, Any]], 
        progress_callback: Optional[Callable] = None,
        max_workers: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Generate CTAs for multiple templates using parallel processing.
        
        Args:
            templates: List of template dictionaries
            progress_callback: Function to report progress (current, total, message)
            max_workers: Maximum number of parallel workers
            
        Returns:
            List of dictionaries with CTA information for each template
        """
        total_templates = len(templates)
        results = []
        processed_count = 0
        
        if progress_callback:
            progress_callback(0, total_templates, "Starting CTA generation...")
        
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_template = {
                executor.submit(self.generate_single_cta, template): template
                for template in templates
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_template):
                template = future_to_template[future]
                try:
                    result = future.result()
                    results.append(result)
                    processed_count += 1
                    
                    # Report progress
                    if progress_callback:
                        template_name = template.get('template_name', f'Template {processed_count}')
                        progress_callback(
                            processed_count - 1, 
                            total_templates, 
                            f"Generated CTA for {template_name}"
                        )
                        
                except Exception as e:
                    # Handle individual template errors
                    error_result = {
                        "cta_text": "Learn More",
                        "cta_url": template.get('product_data', {}).get('Product_URL', ''),
                        "cta_style": "primary",
                        "alt_cta_text": "Get Started",
                        "reasoning": f"Error generating CTA: {str(e)}",
                        "generated_at": datetime.now().isoformat(),
                        "template_name": template.get('template_name', 'Unknown'),
                        "stage": template.get('stage', 'Unknown'),
                        "success": False,
                        "error": str(e)
                    }
                    results.append(error_result)
                    processed_count += 1
                    
                    if progress_callback:
                        progress_callback(
                            processed_count - 1, 
                            total_templates, 
                            f"Error processing template: {str(e)}"
                        )
        
        # Final progress update
        if progress_callback:
            progress_callback(total_templates, total_templates, "CTA generation completed!")

        return results

def load_all_templates() -> List[Dict[str, Any]]:
    """
    Load all templates from the templates directory.

    Returns:
        List of all template dictionaries from all template files
    """
    all_templates = []
    template_files = glob.glob('data/templates/*.json')

    for template_file in template_files:
        try:
            with open(template_file, 'r') as f:
                templates = json.load(f)

            # Add file information to each template
            for template in templates:
                template['template_file'] = template_file
                template['stage_file'] = os.path.basename(template_file).replace('.json', '')
                all_templates.append(template)

        except Exception as e:
            print(f"Error loading templates from {template_file}: {str(e)}")
            continue

    return all_templates

def update_template_with_cta(template_file: str, template_index: int, cta_data: Dict[str, Any]) -> bool:
    """
    Update a specific template with new CTA information.

    Args:
        template_file: Path to the template file
        template_index: Index of the template in the file
        cta_data: CTA data to add to the template

    Returns:
        Boolean indicating success
    """
    try:
        # Load existing templates
        with open(template_file, 'r') as f:
            templates = json.load(f)

        # Update the specific template
        if 0 <= template_index < len(templates):
            # Add CTA data to the template
            templates[template_index]['cta'] = {
                "cta_text": cta_data.get('cta_text', 'Learn More'),
                "cta_url": cta_data.get('cta_url', ''),
                "cta_style": cta_data.get('cta_style', 'primary'),
                "alt_cta_text": cta_data.get('alt_cta_text', 'Get Started'),
                "reasoning": cta_data.get('reasoning', ''),
                "generated_at": cta_data.get('generated_at', datetime.now().isoformat()),
                "success": cta_data.get('success', True),
                "error": cta_data.get('error', None)
            }

            # Save updated templates
            with open(template_file, 'w') as f:
                json.dump(templates, f, indent=4)

            return True
        else:
            print(f"Template index {template_index} out of range for file {template_file}")
            return False

    except Exception as e:
        print(f"Error updating template: {str(e)}")
        return False

def generate_ctas_for_all_templates(progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
    """
    Generate CTAs for all templates in the templates directory.

    Args:
        progress_callback: Function to report progress (current, total, message)

    Returns:
        Dictionary with generation results and statistics
    """
    try:
        # Load all templates
        if progress_callback:
            progress_callback(0, 100, "Loading templates...")

        all_templates = load_all_templates()

        if not all_templates:
            return {
                "success": False,
                "message": "No templates found to process",
                "total_templates": 0,
                "successful_generations": 0,
                "failed_generations": 0,
                "errors": []
            }

        # Initialize CTA generator
        cta_generator = CTAGenerator()

        # Generate CTAs for all templates
        if progress_callback:
            progress_callback(10, 100, f"Generating CTAs for {len(all_templates)} templates...")

        def cta_progress_callback(current, total, message):
            # Convert CTA generation progress to overall progress (10-80%)
            progress = 10 + int((current / total) * 70)
            if progress_callback:
                progress_callback(progress, 100, message)

        cta_results = cta_generator.generate_ctas_parallel(
            all_templates,
            progress_callback=cta_progress_callback,
            max_workers=5
        )

        # Update templates with generated CTAs
        if progress_callback:
            progress_callback(80, 100, "Updating templates with new CTAs...")

        successful_updates = 0
        failed_updates = 0
        errors = []

        # Group results by template file for efficient updating
        template_file_groups = {}
        for i, template in enumerate(all_templates):
            template_file = template['template_file']
            if template_file not in template_file_groups:
                template_file_groups[template_file] = []
            template_file_groups[template_file].append((template, cta_results[i]))

        # Update each template file
        for template_file, template_cta_pairs in template_file_groups.items():
            try:
                # Load the template file
                with open(template_file, 'r') as f:
                    templates = json.load(f)

                # Update templates with CTAs
                for template_data, cta_result in template_cta_pairs:
                    # Find the template index in the file
                    template_name = template_data.get('template_name', '')
                    template_index = -1

                    for idx, file_template in enumerate(templates):
                        if file_template.get('template_name', '') == template_name:
                            template_index = idx
                            break

                    if template_index >= 0:
                        # Add CTA data to the template
                        templates[template_index]['cta'] = {
                            "cta_text": cta_result.get('cta_text', 'Learn More'),
                            "cta_url": cta_result.get('cta_url', ''),
                            "cta_style": cta_result.get('cta_style', 'primary'),
                            "alt_cta_text": cta_result.get('alt_cta_text', 'Get Started'),
                            "reasoning": cta_result.get('reasoning', ''),
                            "generated_at": cta_result.get('generated_at', datetime.now().isoformat()),
                            "success": cta_result.get('success', True),
                            "error": cta_result.get('error', None)
                        }
                        successful_updates += 1
                    else:
                        failed_updates += 1
                        errors.append(f"Could not find template {template_name} in {template_file}")

                # Save updated template file
                with open(template_file, 'w') as f:
                    json.dump(templates, f, indent=4)

            except Exception as e:
                failed_updates += len(template_cta_pairs)
                errors.append(f"Error updating {template_file}: {str(e)}")

        # Final progress update
        if progress_callback:
            progress_callback(100, 100, f"CTA generation completed! Updated {successful_updates} templates.")

        return {
            "success": True,
            "message": f"Successfully generated CTAs for {successful_updates} templates",
            "total_templates": len(all_templates),
            "successful_generations": successful_updates,
            "failed_generations": failed_updates,
            "errors": errors
        }

    except Exception as e:
        if progress_callback:
            progress_callback(100, 100, f"Error: {str(e)}")

        return {
            "success": False,
            "message": f"Error generating CTAs: {str(e)}",
            "total_templates": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "errors": [str(e)]
        }
