"""
Journey builder functionality for OpenEngage.
"""
import plotly.graph_objects as go
from datetime import datetime
from utils.file_utils import load_node_email, log_node_click

def create_journey_tree_plotly(stages, start_idx, highlighted_node=None):
    """Create an interactive journey tree using Plotly"""
    # Fixed node positions for perfect binary tree with depth=2
    node_positions = {
        1: (0, 3),    # Root
        2: (-1, 2),   # Left child of root
        3: (1, 2),    # Right child of root
        4: (-1.5, 1), # Left child of node 2
        5: (-0.5, 1), # Right child of node 2
        6: (0.5, 1),  # Left child of node 3
        7: (1.5, 1)   # Right child of node 3
    }

    # If no node is highlighted, highlight root node
    if highlighted_node is None:
        highlighted_node = 1

    # Define edges for binary tree
    edges = [(1, 2), (1, 3), (2, 4), (2, 5), (3, 6), (3, 7)]

    # Map stages to tree structure
    current_stage = stages[start_idx]
    next_stage = stages[start_idx + 1] if start_idx + 1 < len(stages) else None
    final_stage = stages[start_idx + 2] if start_idx + 2 < len(stages) else None

    # Create node mapping with stages and colors
    node_mapping = {
        1: (current_stage, "#2ECC71", "Current Stage: User is here"),  # Root - Green
        2: (current_stage, "#E74C3C", "Not Achieved: User opened/clicked but hasn't reached goal"),  # Left - Red
        3: (next_stage, "#F1C40F", "Next Stage: User achieved the goal"),     # Right - Yellow
        4: (current_stage, "#E74C3C", "Not Achieved: User opened/clicked but hasn't reached goal"),
        5: (next_stage, "#F1C40F", "Next Stage: User achieved the goal"),
        6: (next_stage, "#E74C3C", "Not Achieved: User opened/clicked but hasn't reached goal"),
        7: (final_stage, "#F1C40F", "Next Stage: User achieved the goal")
    }

    # Create edge traces
    edge_x, edge_y = [], []
    for edge in edges:
        x0, y0 = node_positions[edge[0]]
        x1, y1 = node_positions[edge[1]]
        mid_x = (x0 + x1) / 2
        mid_y = (y0 + y1) / 2 - 0.2
        edge_x.extend([x0, mid_x, x1, None])
        edge_y.extend([y0, mid_y, y1, None])

    edge_trace = go.Scatter(
        x=edge_x, y=edge_y,
        mode='lines',
        line=dict(width=2, color='#888'),
        hoverinfo='none'
    )

    # Create node trace
    node_x, node_y, node_colors, hover_text = [], [], [], []
    node_ids = []

    for node_num in range(1, 8):
        if node_num in node_mapping and node_mapping[node_num][0]:
            stage, color, hover_info = node_mapping[node_num]
            x, y = node_positions[node_num]
            node_x.append(x)
            node_y.append(y)

            # Highlight selected node with a thicker border
            if node_num == highlighted_node:
                line_width = 4
                line_color = '#000'
            else:
                line_width = 2
                line_color = 'white'

            node_colors.append(color)
            hover_text.append(f"{hover_info}<br>Stage: {stage}")
            node_ids.append(f"node_{node_num}")

    node_trace = go.Scatter(
        x=node_x, y=node_y,
        mode='markers+text',
        text=[''] * len(node_x),
        textposition="middle center",
        hoverinfo='text',
        hovertext=hover_text,
        customdata=node_ids,
        marker=dict(
            showscale=False,
            color=node_colors,
            size=40,
            line=dict(width=line_width, color=line_color)
        )
    )

    # Add stage labels
    annotations = []
    for node_num in range(1, 8):
        if node_num in node_mapping and node_mapping[node_num][0]:
            stage, _, _ = node_mapping[node_num]
            x, y = node_positions[node_num]
            annotations.append(
                dict(
                    x=x,
                    y=y - 0.3,
                    text=stage,
                    showarrow=False,
                    font=dict(size=10),
                    xanchor='center'
                )
            )

    fig = go.Figure(
        data=[edge_trace, node_trace],
        layout=go.Layout(
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20, l=20, r=20, t=20),
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[-2, 2]),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[0, 4]),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            width=600,
            height=500,
            clickmode='event',
            annotations=annotations
        )
    )

    return fig, node_ids

def create_user_journey_flow(journey_steps):
    """Create a visual flow of the user's actual journey"""
    if not journey_steps:
        return None

    # Create nodes and edges for the flow
    node_x, node_y, node_text, node_colors, node_sizes = [], [], [], [], []
    node_timestamps = []  # Add timestamps array
    edge_x, edge_y = [], []

    # Fixed horizontal spacing and vertical positions
    x_spacing = 2  # Increased spacing for better visibility
    y_position = 1.2  # Slightly higher to make room for timestamps

    # Track stage nodes and their positions
    stage_positions = {}  # Dict to store x-positions of stage nodes by index
    stage_indices = []  # List to store indices of stage nodes
    current_x = 0

    # First pass: Create stage nodes and calculate positions
    stage_count = 0
    for i, step in enumerate(journey_steps):
        if step['type'] == 'stage':
            x_pos = current_x
            stage_positions[i] = x_pos
            stage_indices.append(i)
            node_x.append(x_pos)
            node_y.append(y_position)
            node_text.append(step['action'])
            node_colors.append('#2ECC71')  # Green for stages
            node_sizes.append(30)  # Larger size for stage nodes
            # Format and add timestamp
            timestamp = datetime.fromisoformat(step['timestamp']).strftime("%H:%M:%S")
            node_timestamps.append(timestamp)
            current_x += x_spacing
            stage_count += 1

    # Second pass: Add event nodes
    event_positions = []  # Store event positions for edge creation
    for i, step in enumerate(journey_steps):
        if step['type'] == 'email':
            # Find the current stage this event belongs to
            current_stage_idx = max((j for j in stage_indices if j <= i), default=stage_indices[0])
            next_stage_idx = min((j for j in stage_indices if j > current_stage_idx), default=stage_indices[-1])

            # Calculate position after the current stage
            current_x = stage_positions[current_stage_idx]
            next_x = stage_positions[next_stage_idx]
            spacing = (next_x - current_x) / 3

            # Position events after the current stage node
            event_x = current_x + (spacing if step['action'] == 'Opened Email' else spacing * 2)

            # Add event node
            node_x.append(event_x)
            node_y.append(y_position)
            node_text.append('📧' if step['action'] == 'Opened Email' else '🖱️')
            node_colors.append('#000000')  # Black for events
            node_sizes.append(15)  # Smaller size for event nodes
            # Format and add timestamp
            timestamp = datetime.fromisoformat(step['timestamp']).strftime("%H:%M:%S")
            node_timestamps.append(timestamp)
            event_positions.append((event_x, y_position))

    # Create edges connecting all nodes in sequence
    all_x_positions = list(zip(node_x, node_y))
    all_x_positions.sort(key=lambda x: x[0])  # Sort by x position

    # Connect all nodes with straight lines
    for i in range(len(all_x_positions) - 1):
        current_pos = all_x_positions[i]
        next_pos = all_x_positions[i + 1]
        edge_x.extend([current_pos[0], next_pos[0], None])
        edge_y.extend([current_pos[1], next_pos[1], None])

    edge_trace = go.Scatter(
        x=edge_x, y=edge_y,
        mode='lines',
        line=dict(width=2, color='#888'),
        hoverinfo='none'
    )

    # Create node trace with text above nodes
    node_trace = go.Scatter(
        x=node_x, y=node_y,
        mode='markers+text',
        text=node_text,
        textposition="top center",
        hoverinfo='text',
        marker=dict(
            showscale=False,
            color=node_colors,
            size=node_sizes,
            line=dict(width=2, color='white')
        )
    )

    # Create timestamp trace with text below nodes
    timestamp_trace = go.Scatter(
        x=node_x,
        y=[y_position - 0.2] * len(node_y),  # Position timestamps below nodes
        mode='text',
        text=node_timestamps,
        textposition="bottom center",
        textfont=dict(
            size=10,
            color='gray'
        ),
        hoverinfo='none'
    )

    # Create figure
    fig = go.Figure(
        data=[edge_trace, node_trace, timestamp_trace],
        layout=go.Layout(
            showlegend=False,
            hovermode='closest',
            margin=dict(b=50, l=20, r=20, t=20),
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False, range=[0.5, 2]),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )
    )

    return fig

def handle_node_click(node_id):
    """Handle node click events and display appropriate email content"""
    import streamlit as st

    if not node_id:
        return

    email = load_node_email(node_id)

    if email:
        node_number = node_id.split('_')[1]  # Extract node number from node_id
        st.write(f"### 📨 Email for Node {node_number}")

        # Create expander for email details
        with st.expander("📋 Email Details", expanded=True):
            # Get user's first name from email content
            first_name = email.get('settings').get('template_context').get('first_name')

            # Get journey steps to find open time if available
            journey_steps = st.session_state.get('journey_steps', [])

            open_time = None
            for step in journey_steps[::-1]:
                if step.get('type') == 'email' and step.get('action') == 'Opened Email':
                    open_time = datetime.fromisoformat(step.get('timestamp')).strftime("%H:%M")
                    break

            # Format best time to send
            generated_time = datetime.fromisoformat(email.get('generated_at', '')).strftime("%H:%M") if email.get('generated_at') else "N/A"
            best_time = open_time if open_time else generated_time

            # Create two columns for details
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Product Name:**")
                st.write(email.get('settings').get('template_context').get('base_template').get('product_data', {}).get('Product_Name', 'N/A'))

                st.markdown("**User Name:**")
                st.write(first_name)

                st.markdown("**Stage:**")
                st.write(email.get('stage', 'N/A'))

            with col2:
                st.markdown("**User Behavior:**")
                st.write(email.get('settings').get('template_context').get('user_behavior', 'N/A'))

                st.markdown("**Best Time to Send:**")
                st.write(best_time)

        st.write(f"**Subject:** {email.get('subject', 'N/A')}")

        # Display pre-header if available
        if 'pre_header' in email and email['pre_header']:
            st.write(f"**Pre-header:** {email.get('pre_header', '')}")

        # Create tabs for plain text and HTML view
        text_tab, html_tab = st.tabs(["Plain Text", "HTML Preview"])

        with text_tab:
            # Display main content in a clean box
            st.markdown("""
            <style>
            .email-content {
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                border: 1px solid #ddd;
                margin: 10px 0;
            }
            </style>
            """, unsafe_allow_html=True)

            st.markdown(f"""
            <div class="email-content">
            {email.get('content', 'N/A')}
            </div>
            """, unsafe_allow_html=True)

        with html_tab:
            # Display HTML content if available
            if 'html_content' in email and email['html_content']:
                # Create a scrollable container for the HTML preview
                st.markdown("""
                <style>
                .email-preview-container {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    height: 600px;
                    overflow-y: auto;
                    background-color: white;
                    padding: 0;
                }
                </style>
                """, unsafe_allow_html=True)

                # Wrap the HTML component in a scrollable div
                html_content = f"""
                <div class="email-preview-container">
                    {email['html_content']}
                </div>
                """

                # Display the HTML content in the scrollable container
                st.components.v1.html(html_content, height=620, scrolling=True)

                # Add a download button for the HTML content
                st.download_button(
                    label="Download HTML Email",
                    data=email['html_content'],
                    file_name=f"email_{email.get('stage', 'unknown')}.html",
                    mime="text/html",
                    key=f"download_journey_email_{node_id}"
                )
            else:
                st.info("No HTML content available for this email. HTML content is generated when the journey is created.")

        # Log the click
        log_node_click(node_id, st.session_state.get("session_id", "unknown"))

# Cache for storing embeddings to avoid redundant API calls
_embedding_cache = {}

def find_similar_product(activity, products):
    """Find most similar product based on activity description using OpenAI embeddings"""
    import os
    import hashlib
    from openai import OpenAI
    import streamlit as st

    # Initialize OpenAI client
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Function to get embeddings for a text with caching
    def get_embedding(text):
        if not text or text.strip() == "":
            text = "No description available"

        # Create a hash of the text to use as cache key
        text_hash = hashlib.md5(text.encode()).hexdigest()

        # Check if we have this embedding cached
        global _embedding_cache
        if text_hash in _embedding_cache:
            return _embedding_cache[text_hash]

        try:
            # Show a small spinner in Streamlit when getting embeddings
            with st.spinner("Computing semantic similarity..."):
                response = client.embeddings.create(
                    model="text-embedding-3-small",
                    input=text,
                    encoding_format="float"
                )
                embedding = response.data[0].embedding

                # Cache the result
                _embedding_cache[text_hash] = embedding
                return embedding
        except Exception as e:
            print(f"Error getting embedding: {str(e)}")
            # Return a zero vector as fallback
            return [0.0] * 1536  # Default dimension for text-embedding-3-small

    # Function to calculate cosine similarity between two vectors
    def cosine_similarity(vec1, vec2):
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        norm_a = sum(a * a for a in vec1) ** 0.5
        norm_b = sum(b * b for b in vec2) ** 0.5

        if norm_a == 0 or norm_b == 0:
            return 0.0

        return dot_product / (norm_a * norm_b)

    # Get embedding for the activity
    activity_embedding = get_embedding(activity)

    # Get embeddings for each product summary
    product_embeddings = []
    product_texts = []

    print(f"\n===== SEMANTIC PRODUCT MATCHING =====\n")
    print(f"User activity/behavior: {activity[:100]}..." if len(activity) > 100 else f"User activity/behavior: {activity}")
    print(f"Number of products to compare: {len(products)}")

    for i, product in enumerate(products):
        # Combine product details for better context
        product_name = product.get('Product_Name', 'Unknown Product')
        company_name = product.get('Company_Name', 'Unknown Company')
        product_type = product.get('Type_of_Product', '')
        product_summary = product.get('Product_Summary', '')

        # Build a rich context for the product
        product_text = f"Product: {product_name}\n"
        product_text += f"Company: {company_name}\n"
        if product_type:
            product_text += f"Type: {product_type}\n"
        product_text += f"Summary: {product_summary}\n"

        # Add features if available
        if 'Product_Features' in product and product['Product_Features']:
            product_text += "Features:\n"
            for feature in product['Product_Features']:
                product_text += f"- {feature}\n"

        print(f"\nProduct {i+1}: {product_name}")
        print(f"Text length: {len(product_text)} characters")

        product_texts.append(product_text)
        product_embeddings.append(get_embedding(product_text))

    # Calculate similarities
    similarities = [cosine_similarity(activity_embedding, prod_emb) for prod_emb in product_embeddings]

    # Get most similar product
    if not similarities or len(similarities) == 0:
        print("\nNo similarities calculated. Returning default product.")
        return products[0] if products else None, 0.0

    # Create a list of (index, similarity) tuples and sort by similarity
    similarity_pairs = [(i, sim) for i, sim in enumerate(similarities)]
    similarity_pairs.sort(key=lambda x: x[1], reverse=True)

    # Get the most similar product
    most_similar_idx = similarity_pairs[0][0]
    similarity_score = similarity_pairs[0][1]

    # Print detailed results
    print("\n----- Similarity Results -----")
    for i, (idx, score) in enumerate(similarity_pairs):
        product_name = products[idx].get('Product_Name', 'Unknown')
        print(f"{i+1}. {product_name}: {score:.4f}" + (" (BEST MATCH)" if idx == most_similar_idx else ""))

    print(f"\nBest match: {products[most_similar_idx].get('Product_Name', 'Unknown')}")
    print(f"Similarity score: {similarity_score:.4f}")
    print("\n==============================\n")

    return products[most_similar_idx], similarity_score
