#!/usr/bin/env python3
"""
Test script for the CTA Generator functionality.
This script tests the CTA generation without requiring the full Streamlit app.
"""

import os
import sys
import json
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_cta_generator():
    """Test the CTA generator with sample template data."""
    
    # Import the CTA generator
    try:
        from openengage.core.cta_generator import CTAGenerator, load_all_templates
        print("✅ Successfully imported CTA generator")
    except ImportError as e:
        print(f"❌ Failed to import CTA generator: {e}")
        return False
    
    # Create a sample template for testing
    sample_template = {
        "template_name": "Test_Template_1",
        "template": {
            "subject": "Unlock Your Future with Generative AI!",
            "body": "Hi there,\n\nAre you ready to take the next step in your career? The GenAI Pinnacle Plus Program is designed to help you gain expertise in Generative AI through over 300 hours of immersive learning.\n\nOne of the standout features of this program is the 50+ industry-aligned projects. These projects provide hands-on experience that bridges theoretical knowledge with practical skills, preparing you for real-world challenges.\n\nWith personalized 1:1 mentorship from Generative AI experts, you'll receive tailored guidance to excel in your learning journey. Don't miss out on this opportunity to upskill and lead in the AI revolution.\n\nBest Regards,\nTeam Analytics Vidhya"
        },
        "product_data": {
            "Product_Name": "GenAI Pinnacle Plus Program",
            "Company_Name": "Analytics Vidhya",
            "Product_URL": "https://www.analyticsvidhya.com/pinnacleplus/",
            "Type_of_Product": "Online Educational Program"
        },
        "settings": {
            "tone_of_voice": "Informative, Engaging, Supportive, Professional",
            "style": "friendly",
            "brand_personality": "Sage"
        },
        "stage": "New Visitor"
    }
    
    # Test single CTA generation
    print("\n🧪 Testing single CTA generation...")
    
    try:
        cta_generator = CTAGenerator()
        result = cta_generator.generate_single_cta(sample_template)
        
        print("✅ Single CTA generation successful!")
        print(f"   CTA Text: {result.get('cta_text', 'N/A')}")
        print(f"   CTA URL: {result.get('cta_url', 'N/A')}")
        print(f"   CTA Style: {result.get('cta_style', 'N/A')}")
        print(f"   Success: {result.get('success', False)}")
        
        if result.get('error'):
            print(f"   Error: {result['error']}")
            
    except Exception as e:
        print(f"❌ Single CTA generation failed: {e}")
        return False
    
    # Test parallel CTA generation
    print("\n🧪 Testing parallel CTA generation...")
    
    try:
        # Create multiple sample templates
        sample_templates = []
        for i in range(3):
            template = sample_template.copy()
            template['template_name'] = f"Test_Template_{i+1}"
            template['template'] = template['template'].copy()
            template['template']['subject'] = f"Test Subject {i+1}"
            sample_templates.append(template)
        
        def progress_callback(current, total, message):
            print(f"   Progress: {current}/{total} - {message}")
        
        results = cta_generator.generate_ctas_parallel(
            sample_templates, 
            progress_callback=progress_callback,
            max_workers=2
        )
        
        print("✅ Parallel CTA generation successful!")
        print(f"   Generated {len(results)} CTAs")
        
        for i, result in enumerate(results):
            print(f"   Template {i+1}: {result.get('cta_text', 'N/A')} - Success: {result.get('success', False)}")
            
    except Exception as e:
        print(f"❌ Parallel CTA generation failed: {e}")
        return False
    
    print("\n✅ All CTA generator tests passed!")
    return True

def test_template_loading():
    """Test loading templates from the data directory."""
    print("\n🧪 Testing template loading...")
    
    try:
        from openengage.core.cta_generator import load_all_templates
        
        # Check if templates directory exists
        if not os.path.exists('data/templates'):
            print("⚠️  No templates directory found - this is expected for a fresh installation")
            return True
        
        templates = load_all_templates()
        print(f"✅ Successfully loaded {len(templates)} templates")
        
        if templates:
            print("   Sample template names:")
            for i, template in enumerate(templates[:3]):  # Show first 3
                print(f"   - {template.get('template_name', f'Template {i+1}')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Template loading failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting CTA Generator Tests")
    print("=" * 50)
    
    # Set up environment
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Warning: OPENAI_API_KEY not found in environment")
        print("   CTA generation will fail without a valid API key")
        print("   Set your API key in .env file or environment variables")
    
    # Run tests
    success = True
    
    success &= test_template_loading()
    
    if os.getenv("OPENAI_API_KEY"):
        success &= test_cta_generator()
    else:
        print("\n⏭️  Skipping CTA generation tests (no API key)")
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests completed successfully!")
    else:
        print("❌ Some tests failed!")
    
    print("\n📝 Next steps:")
    print("1. Ensure OpenAI API key is set in your .env file")
    print("2. Generate some email templates using the UI")
    print("3. Use the 'Generate new CTA' button in Template Verification")
    print("4. Check that CTAs appear in the template display")
